<template>
  <NjPage
    v-bind="$attrs"
    :title="
      localSimulation?.id !== 0
        ? (localSimulation?.stepId === 0 || (createOperation && localSimulation.simulationName)
            ? 'Simulation : ' + localSimulation?.simulationName
            : localSimulation?.operationName) +
          ' - ' +
          localSimulation?.standardizedOperationSheet.operationCode
        : localSimulation?.standardizedOperationSheet.operationCode
    "
    :can-go-back="{
      name:
        localSimulation.id === 0
          ? createOperation
            ? 'OperationOneNewView'
            : 'CreateSimulationView'
          : localSimulation.stepId == 0 || (createOperation && localSimulation.simulationName)
            ? 'SimulationOneView'
            : 'OperationOneView',
      params: { id: localSimulation.id },
    }"
    :error="error || pageError"
  >
    <template #header-actions>
      <DoublonDialog />

      <template v-if="localSimulation.id > 0 || createOperation">
        <NjBtn
          v-if="createOperation && !!simulation?.id"
          variant="outlined"
          :loading="savingOperation.loading"
          :disabled="missingEntity"
          @click="router.push({ name: 'SimulationOneView', params: { id: simulation.id } })"
        >
          Annuler
        </NjBtn>
        <NjBtn
          v-if="props.createOperation"
          :loading="savingOperation.loading"
          :disabled="missingEntity || !operationFormRef?.valuations"
          @click="saveOperation"
        >
          Enregistrer l'opération
        </NjBtn>
        <NjBtn v-else :loading="savingOperation.loading" @click="saveOperation"> Enregistrer les modifications </NjBtn>
      </template>
      <template v-else>
        <NjBtn v-if="operationFormRef?.edit" variant="outlined" @click="operationFormRef!.cancelSaveASimulation"
          >Annuler</NjBtn
        >
        <NjBtn v-if="!operationFormRef?.edit" :disabled="missingEntity" @click="operationFormRef?.createSimulation">
          Créer une simulation
        </NjBtn>
        <NjBtn
          v-if="operationFormRef?.edit"
          :loading="savingOperation.loading"
          :disabled="missingEntity || !operationFormRef?.valuations"
          @click="saveOperation"
        >
          Enregistrer la simulation
        </NjBtn>
      </template>
    </template>
    <template #subtitle>
      <div>
        {{ localSimulation?.standardizedOperationSheet.description }}
      </div>
    </template>
    <template #body>
      <CardDialog v-model="controlOrderDialog" width="40%" title="Arrêté contrôle" :closeable="false">
        <VAlert type="info" variant="outlined">
          <span
            v-if="
              new Date(simulation.standardizedOperationSheet.controlOrderStartDate!) <=
              new Date(simulation.estimatedCommitmentDate)
            "
          >
            L'opération {{ simulation.standardizedOperationSheet.operationCode }} est soumise à l'arrêté contrôle.
          </span>
          <span v-else>
            L'opération {{ simulation.standardizedOperationSheet.operationCode }} est soumise à l'arrêté contrôle si la
            date d'engagement est à compter du
            <b>{{ formatHumanReadableLocalDate(simulation.standardizedOperationSheet.controlOrderStartDate) }}</b
            >.
          </span>
        </VAlert>
        <template #actions>
          <NjBtn @click="controlOrderDialog = false"> Ok </NjBtn>
        </template>
      </CardDialog>
      <VCard>
        <OperationForm
          v-if="!loading"
          ref="operationFormRef"
          v-model:saving="savingOperation"
          :operation="localSimulation"
          :edit="edit"
          :create-operation="createOperation"
          expanded-detail
        />
        <VProgressCircular v-else color="primary" class="ms-4" indeterminate />
      </VCard>
    </template>
  </NjPage>
</template>

<script lang="ts" setup>
import CardDialog from '@/components/CardDialog.vue'
import NjPage from '@/components/NjPage.vue'
import { useUserStore } from '@/stores/user'
import { formatHumanReadableLocalDate } from '@/types/date'
import type { Operation } from '@/types/operation'
import type { PropType } from 'vue'
import type { VAlert } from 'vuetify/components'
import DoublonDialog from './DoublonDialog.vue'
import OperationForm from './operation/OperationForm.vue'

const props = defineProps({
  simulation: {
    type: Object as PropType<Operation>,
    required: true,
  },
  loading: Boolean,
  edit: Boolean,
  error: String,
  createOperation: Boolean,
})

const userStore = useUserStore()
const router = useRouter()

//rules
const localSimulation = ref<Operation>(makeEmptyOperation())
const controlOrderDialog = ref(false)

const pageError = ref<string>()

// Operation Form
const savingOperation = ref(emptyValue())
const operationFormRef = ref<typeof OperationForm | null>(null)
const saveOperation = () => {
  operationFormRef.value!.saveOperation()
}

// load
watch(
  () => props.simulation,
  (v) => {
    localSimulation.value = v
    controlOrderDialog.value = props.createOperation && !!v.standardizedOperationSheet.controlOrderStartDate
  },
  {
    immediate: true,
    deep: true,
  }
)

const missingEntity = computed(() => userStore.currentUser.entities.length <= 0)
</script>
